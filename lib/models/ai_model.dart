enum ModelCapability {
  vision('视觉', 'vision'),
  embedding('嵌入', 'embedding'),
  reasoning('推理', 'reasoning'),
  tools('工具', 'tools');

  const ModelCapability(this.displayName, this.id);
  final String displayName;
  final String id;
}

class AiModel {
  final String id;
  final String name;
  final String displayName;
  final List<ModelCapability> capabilities;
  final Map<String, dynamic> metadata; // 额外的模型信息，如上下文长度等
  final bool isEnabled;
  final DateTime createdAt;
  final DateTime updatedAt;

  const AiModel({
    required this.id,
    required this.name,
    this.displayName = '',
    this.capabilities = const [ModelCapability.reasoning],
    this.metadata = const {},
    this.isEnabled = true,
    required this.createdAt,
    required this.updatedAt,
  });

  String get effectiveDisplayName => displayName.isEmpty ? name : displayName;

  AiModel copyWith({
    String? id,
    String? name,
    String? displayName,
    List<ModelCapability>? capabilities,
    Map<String, dynamic>? metadata,
    bool? isEnabled,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return AiModel(
      id: id ?? this.id,
      name: name ?? this.name,
      displayName: displayName ?? this.displayName,
      capabilities: capabilities ?? this.capabilities,
      metadata: metadata ?? this.metadata,
      isEnabled: isEnabled ?? this.isEnabled,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'displayName': displayName,
      'capabilities': capabilities.map((c) => c.id).toList(),
      'metadata': metadata,
      'isEnabled': isEnabled,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  factory AiModel.fromJson(Map<String, dynamic> json) {
    return AiModel(
      id: json['id'] as String,
      name: json['name'] as String,
      displayName: json['displayName'] as String? ?? '',
      capabilities:
          (json['capabilities'] as List<dynamic>?)
              ?.map(
                (c) => ModelCapability.values.firstWhere(
                  (cap) => cap.id == c,
                  orElse: () => ModelCapability.reasoning,
                ),
              )
              .toList() ??
          [ModelCapability.reasoning],
      metadata: json['metadata'] as Map<String, dynamic>? ?? {},
      isEnabled: json['isEnabled'] as bool? ?? true,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is AiModel &&
          runtimeType == other.runtimeType &&
          id == other.id &&
          name == other.name &&
          displayName == other.displayName &&
          isEnabled == other.isEnabled;

  @override
  int get hashCode =>
      id.hashCode ^ name.hashCode ^ displayName.hashCode ^ isEnabled.hashCode;
}
