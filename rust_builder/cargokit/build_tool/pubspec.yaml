# This is copied from Cargokit (which is the official way to use it currently)
# Details: https://fzyzcjy.github.io/flutter_rust_bridge/manual/integrate/builtin

name: build_tool
description: Cargokit build_tool. Facilitates the build of Rust crate during Flutter application build.
publish_to: none
version: 1.0.0

environment:
  sdk: ">=3.0.0 <4.0.0"

# Add regular dependencies here.
dependencies:
  # these are pinned on purpose because the bundle_tool_runner doesn't have
  # pubspec.lock. See run_build_tool.sh
  logging: 1.2.0
  path: 1.8.0
  version: 3.0.0
  collection: 1.18.0
  ed25519_edwards: 0.3.1
  hex: 0.2.0
  yaml: 3.1.2
  source_span: 1.10.0
  github: 9.17.0
  args: 2.4.2
  crypto: 3.0.3
  convert: 3.1.1
  http: 1.1.0
  toml: 0.14.0

dev_dependencies:
  lints: ^2.1.0
  test: ^1.24.0
