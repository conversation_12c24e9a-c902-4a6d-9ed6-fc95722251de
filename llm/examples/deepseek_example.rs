// Import required modules from the LLM library for DeepSeek integration
use llm::{
    builder::{LL<PERSON><PERSON>end, LLMBuilder}, // Builder pattern components
    chat::ChatMessage,                 // Chat-related structures
};

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // Get DeepSeek API key from environment variable or use test key as fallback
    let api_key = "sk-44b0c50ce10044a098833dc6415f787e".to_string();

    // Initialize and configure the LLM client
    let llm = LLMBuilder::new()
        .backend(LLMBackend::DeepSeek) // Use DeepSeek as the LLM provider
        .system("You are a helpful assistant and you response only with words begin with deepseek_")
        .api_key(api_key) // Set the API key
        .model("deepseek-reasoner") // Use DeepSeek Chat model
        .timeout_seconds(1200)
        .temperature(0.7) // Control response randomness (0.0-1.0)
        .stream(false) // Disable streaming responses
        .build()
        .expect("Failed to build LLM (DeepSeek)");

    // Prepare conversation history with example messages
    let messages = vec![
        ChatMessage::user()
            .content("Tell me that you love cats")
            .build(),
        ChatMessage::assistant()
            .content("I am an assistant, I cannot love cats but I can love dogs")
            .build(),
        ChatMessage::user()
            .content("Tell me that you love dogs in 2000 chars")
            .build(),
    ];

    // Send chat request and handle the response
    match llm.chat(&messages).await {
        Ok(text) => println!("Chat response:\n{}", text),
        Err(e) => eprintln!("Chat error: {}", e),
    }

    Ok(())
}
