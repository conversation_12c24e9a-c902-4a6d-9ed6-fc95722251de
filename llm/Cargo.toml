[package]
name = "llm"
version = "1.2.9"
edition = "2021"
description = "A Rust library unifying multiple LLM backends."
license = "MIT"
authors = [
    "<PERSON> <<EMAIL>>",
    "<PERSON> <<EMAIL>>",
]
repository = "https://github.com/graniet/llm"
documentation = "https://docs.rs/llm"
homepage = "https://github.com/graniet/llm"
default-run = "llm"

[features]
default = ["cli"]
full = [
    "openai",
    "anthropic",
    "ollama",
    "deepseek",
    "xai",
    "phind",
    "google",
    "groq",
    "azure_openai",
    "api",
    "elevenlabs",
]
openai = []
anthropic = []
ollama = []
deepseek = []
xai = []
phind = []
google = []
groq = []
azure_openai = []
cli = ["full", "dep:clap", "dep:rustyline", "dep:colored", "dep:spinners"]
api = ["dep:axum", "dep:tower-http", "dep:uuid"]
elevenlabs = []
rodio = ["dep:rodio"]
logging = ["dep:env_logger"]

[dependencies]
serde = { version = "1.0", features = ["derive"] }
reqwest = { version = "0.12.12", features = ["json", "multipart", "stream"] }
serde_json = "1.0"
async-trait = "0.1"
axum = { version = "0.7", optional = true, features = ["json"] }
tokio = { version = "1.0", features = ["full"] }
tower-http = { version = "0.5", optional = true, features = ["cors"] }
uuid = { version = "1.0", optional = true, features = ["v4"] }
base64 = "0.22.1"
futures = "0.3"
clap = { version = "4", features = ["derive"], optional = true }
rustyline = { version = "15", optional = true }
colored = { version = "3.0.0", optional = true }
spinners = { version = "4.1", optional = true }
serde_yaml = "0.9"
dirs = "6.0.0"
either = { version = "1.15.0", features = ["serde"] }
rodio = { version = "0.20.0", features = ["mp3", "wav"], optional = true }
log = "0.4"
env_logger = { version = "0.11", optional = true }

[[bin]]
name = "llm"
path = "src/bin/llm-cli.rs"
required-features = ["cli"]

[dev-dependencies]
tokio = { version = "1.0", features = ["macros", "rt-multi-thread"] }
rodio = { version = "0.20.1", default-features = false, features = ["symphonia-all"]}

