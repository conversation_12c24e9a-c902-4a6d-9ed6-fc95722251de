{"rustc": 16591470773350601817, "features": "[\"cors\", \"default\"]", "declared_features": "[\"add-extension\", \"async-compression\", \"auth\", \"base64\", \"catch-panic\", \"compression-br\", \"compression-deflate\", \"compression-full\", \"compression-gzip\", \"compression-zstd\", \"cors\", \"decompression-br\", \"decompression-deflate\", \"decompression-full\", \"decompression-gzip\", \"decompression-zstd\", \"default\", \"follow-redirect\", \"fs\", \"full\", \"futures-core\", \"futures-util\", \"httpdate\", \"iri-string\", \"limit\", \"map-request-body\", \"map-response-body\", \"metrics\", \"mime\", \"mime_guess\", \"normalize-path\", \"percent-encoding\", \"propagate-header\", \"redirect\", \"request-id\", \"sensitive-headers\", \"set-header\", \"set-status\", \"timeout\", \"tokio\", \"tokio-util\", \"tower\", \"trace\", \"tracing\", \"util\", \"uuid\", \"validate-request\"]", "target": 14850331575045365232, "profile": 2241668132362809309, "path": 9524350107235461577, "deps": [[784494742817713399, "tower_service", false, 921410483914319120], [1906322745568073236, "pin_project_lite", false, 15751135382563959418], [7712452662827335977, "tower_layer", false, 6534595002414969486], [7896293946984509699, "bitflags", false, 3903921708477240841], [9010263965687315507, "http", false, 2341963323735707095], [14084095096285906100, "http_body", false, 11457077013012887498], [16066129441945555748, "bytes", false, 8184079702603890642], [16900715236047033623, "http_body_util", false, 875475143420948853]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tower-http-f768e167f614c343\\dep-lib-tower_http", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}