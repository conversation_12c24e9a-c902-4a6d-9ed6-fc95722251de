{"rustc": 16591470773350601817, "features": "[\"log\", \"std\"]", "declared_features": "[\"async-await\", \"attributes\", \"default\", \"log\", \"log-always\", \"max_level_debug\", \"max_level_error\", \"max_level_info\", \"max_level_off\", \"max_level_trace\", \"max_level_warn\", \"release_max_level_debug\", \"release_max_level_error\", \"release_max_level_info\", \"release_max_level_off\", \"release_max_level_trace\", \"release_max_level_warn\", \"std\", \"tracing-attributes\", \"valuable\"]", "target": 5568135053145998517, "profile": 11202463608144111571, "path": 15760570039277490425, "deps": [[1906322745568073236, "pin_project_lite", false, 15751135382563959418], [5986029879202738730, "log", false, 881829371126356465], [11033263105862272874, "tracing_core", false, 38170993243767250]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tracing-fb544580623f09e0\\dep-lib-tracing", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}