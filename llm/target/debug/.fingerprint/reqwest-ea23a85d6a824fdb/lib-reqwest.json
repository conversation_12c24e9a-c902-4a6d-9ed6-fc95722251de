{"rustc": 16591470773350601817, "features": "[\"__tls\", \"charset\", \"default\", \"default-tls\", \"h2\", \"http2\", \"json\", \"multipart\", \"stream\", \"system-proxy\"]", "declared_features": "[\"__rustls\", \"__rustls-ring\", \"__tls\", \"blocking\", \"brotli\", \"charset\", \"cookies\", \"default\", \"default-tls\", \"deflate\", \"gzip\", \"h2\", \"hickory-dns\", \"http2\", \"http3\", \"json\", \"macos-system-configuration\", \"multipart\", \"native-tls\", \"native-tls-alpn\", \"native-tls-vendored\", \"rustls-tls\", \"rustls-tls-manual-roots\", \"rustls-tls-manual-roots-no-provider\", \"rustls-tls-native-roots\", \"rustls-tls-native-roots-no-provider\", \"rustls-tls-no-provider\", \"rustls-tls-webpki-roots\", \"rustls-tls-webpki-roots-no-provider\", \"socks\", \"stream\", \"system-proxy\", \"trust-dns\", \"zstd\"]", "target": 8885864859914201979, "profile": 447021486742529345, "path": 15257297935374822728, "deps": [[40386456601120721, "percent_encoding", false, 1401001513525780257], [95042085696191081, "ipnet", false, 8967654467096988442], [418947936956741439, "h2", false, 8344758288710317393], [784494742817713399, "tower_service", false, 921410483914319120], [970965535607393401, "hyper_util", false, 3270177334046889866], [1288403060204016458, "tokio_util", false, 6970259852457749840], [1906322745568073236, "pin_project_lite", false, 15751135382563959418], [2054153378684941554, "tower_http", false, 6776676923953043356], [2517136641825875337, "sync_wrapper", false, 13512842734903795619], [2883436298747778685, "rustls_pki_types", false, 16985562484613422105], [3150220818285335163, "url", false, 3451720119061472870], [3722963349756955755, "once_cell", false, 13959756635494716824], [5695049318159433696, "tower", false, 11449938033268170540], [5986029879202738730, "log", false, 881829371126356465], [7620660491849607393, "futures_core", false, 67156897965447383], [9010263965687315507, "http", false, 2341963323735707095], [9538054652646069845, "tokio", false, 3654077324401139434], [9689903380558560274, "serde", false, 3376193961338184148], [10229185211513642314, "mime", false, 7119999706744330589], [10629569228670356391, "futures_util", false, 2619930252757155693], [11957360342995674422, "hyper", false, 14202428573192241704], [12186126227181294540, "tokio_native_tls", false, 13335341331693347291], [13077212702700853852, "base64", false, 553302298209367549], [14084095096285906100, "http_body", false, 11457077013012887498], [14564311161534545801, "encoding_rs", false, 12716644789980104792], [15367738274754116744, "serde_json", false, 4978703312198190535], [16066129441945555748, "bytes", false, 8184079702603890642], [16542808166767769916, "serde_urlencoded", false, 2802959443517389462], [16785601910559813697, "native_tls_crate", false, 15618980983973964642], [16900715236047033623, "http_body_util", false, 875475143420948853], [18071510856783138481, "mime_guess", false, 12059256955227916822], [18273243456331255970, "hyper_tls", false, 7199311071884256345]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\reqwest-ea23a85d6a824fdb\\dep-lib-reqwest", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}