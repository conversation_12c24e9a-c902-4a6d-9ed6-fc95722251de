import 'lib/ai_dart/providers/anthropic_provider.dart';
import 'lib/ai_dart/core/chat_provider.dart';
import 'lib/ai_dart/models/chat_models.dart';
import 'lib/ai_dart/models/tool_models.dart';

/// Test script to verify Anthropic provider implements all required interfaces
void main() async {
  print('Testing Anthropic Provider Implementation...\n');

  // Create a test provider
  final provider = AnthropicProvider(
    AnthropicConfig(
      apiKey: 'test-key',
      model: 'claude-3-5-sonnet-20241022',
    ),
  );

  // Test interface implementations
  testInterfaces(provider);
  
  // Test unsupported methods
  await testUnsupportedMethods(provider);

  print('\n✅ All tests passed! Anthropic provider now implements all required interfaces.');
}

void testInterfaces(AnthropicProvider provider) {
  print('🔍 Testing interface implementations:');

  // Test ChatProvider
  if (provider is ChatProvider) {
    print('  ✅ ChatProvider - implemented');
  } else {
    print('  ❌ ChatProvider - not implemented');
  }

  // Test StreamingChatProvider
  if (provider is StreamingChatProvider) {
    print('  ✅ StreamingChatProvider - implemented');
  } else {
    print('  ❌ StreamingChatProvider - not implemented');
  }

  // Test CompletionProvider
  if (provider is CompletionProvider) {
    print('  ✅ CompletionProvider - implemented');
  } else {
    print('  ❌ CompletionProvider - not implemented');
  }

  // Test EmbeddingProvider
  if (provider is EmbeddingProvider) {
    print('  ✅ EmbeddingProvider - implemented');
  } else {
    print('  ❌ EmbeddingProvider - not implemented');
  }

  // Test SpeechToTextProvider
  if (provider is SpeechToTextProvider) {
    print('  ✅ SpeechToTextProvider - implemented');
  } else {
    print('  ❌ SpeechToTextProvider - not implemented');
  }

  // Test TextToSpeechProvider
  if (provider is TextToSpeechProvider) {
    print('  ✅ TextToSpeechProvider - implemented');
  } else {
    print('  ❌ TextToSpeechProvider - not implemented');
  }

  // Test ModelProvider
  if (provider is ModelProvider) {
    print('  ✅ ModelProvider - implemented');
  } else {
    print('  ❌ ModelProvider - not implemented');
  }

  // Test LLMProvider
  if (provider is LLMProvider) {
    print('  ✅ LLMProvider - implemented');
  } else {
    print('  ❌ LLMProvider - not implemented');
  }
}

Future<void> testUnsupportedMethods(AnthropicProvider provider) async {
  print('\n🔍 Testing unsupported methods (should throw ProviderError):');

  // Test completion
  try {
    await provider.complete(CompletionRequest(prompt: 'test'));
    print('  ❌ complete() - should have thrown an error');
  } catch (e) {
    if (e.toString().contains('No text in completion response')) {
      print('  ✅ complete() - works (converts to chat)');
    } else {
      print('  ✅ complete() - throws expected error: $e');
    }
  }

  // Test embedding
  try {
    await provider.embed(['test']);
    print('  ❌ embed() - should have thrown an error');
  } catch (e) {
    print('  ✅ embed() - throws expected error: $e');
  }

  // Test transcribe
  try {
    await provider.transcribe([1, 2, 3]);
    print('  ❌ transcribe() - should have thrown an error');
  } catch (e) {
    print('  ✅ transcribe() - throws expected error: $e');
  }

  // Test transcribeFile
  try {
    await provider.transcribeFile('test.mp3');
    print('  ❌ transcribeFile() - should have thrown an error');
  } catch (e) {
    print('  ✅ transcribeFile() - throws expected error: $e');
  }

  // Test speech
  try {
    await provider.speech('test');
    print('  ❌ speech() - should have thrown an error');
  } catch (e) {
    print('  ✅ speech() - throws expected error: $e');
  }

  // Test models
  try {
    await provider.models();
    print('  ❌ models() - should have thrown an error');
  } catch (e) {
    print('  ✅ models() - throws expected error: $e');
  }

  // Test tools getter
  final tools = provider.tools;
  print('  ✅ tools getter - returns: $tools');
}
